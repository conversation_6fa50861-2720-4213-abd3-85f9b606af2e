CompileFlags:
  Add:
    - '-Id:\BLE_SinglePhase_Electric_Meter\phy_ble\softwore\PHY6222_gateway/RTE/_Target_1'
    - '-IC:\Keil_v5\ARM\ARMCC\include'
    - '-IC:\Keil_v5\ARM\ARMCC\include\rw'
    - '-Icomponents\inc'
    - '-Icomponents\ble\controller\include'
    - '-Icomponents\osal\include'
    - '-Icomponents\common'
    - '-Icomponents\ble\include'
    - '-Icomponents\ble\hci'
    - '-Icomponents\ble\host'
    - '-Icomponents\Profiles\DevInfo'
    - '-Icomponents\Profiles\SimpleProfile'
    - '-Icomponents\Profiles\Roles'
    - '-Icomponents\driver\common'
    - '-Icomponents\driver\clock'
    - '-Icomponents\driver\gpio'
    - '-Icomponents\driver\pwrmgr'
    - '-Icomponents\driver\log'
    - '-Icomponents\driver\uart'
    - '-Icomponents\driver\flash'
    - '-Icomponents\driver\pwm'
    - '-Iexample\ble_mesh\mesh_gateway\source'
    - '-Iexample\ble_mesh\mesh_gateway\source\bleMesh'
    - '-Icomponents\ethermind\external\crypto\aes'
    - '-Icomponents\ethermind\mesh\export\include'
    - '-Icomponents\ethermind\mesh\export\bearer'
    - '-Icomponents\ethermind\mesh\export\platforms\ext'
    - '-Icomponents\ethermind\osal\src\phyos'
    - '-Icomponents\ethermind\utils\include'
    - '-Icomponents\ethermind\mesh\export\appl'
    - '-Icomponents\osal\snv'
    - '-Icomponents\ethermind\external\crypto\asm_ecdh_p256'
    - '-Icomponents\ethermind\lib'
    - '-Icomponents\ethermind\mesh\export\cbtimer'
    - '-Icomponents\arch\cm0'
    - '-Imisc'
    - '-Icomponents\ble\controller'
    - '-Icomponents\libraries\crc16'
    - '-Icomponents\driver\timer'
    - '-Icomponents\ethermind\mesh\export\climodel'
    - '-Icomponents\libraries\fs'
    - '-Icomponents\libraries\cli'
    - '-Icomponents\ethermind\mesh\export\vendormodel'
    - '-Icomponents\ethermind\mesh\export\vendormodel\client'
    - '-Icomponents\ethermind\mesh\export\vendormodel\server'
    - '-Icomponents\ethermind\platforms'
    - '-Icomponents\ethermind\platforms\interfaces\crypto'
    - '-Icomponents\ethermind\platforms\mesh'
    - '-Icomponents\driver\led_light'
    - '-DCFG_CP'
    - '-DOSAL_CBTIMER_NUM_TASKS=1'
    - '-DMTU_SIZE=247'
    - '-DOSALMEM_METRICS=0'
    - '-DDEBUG_INFO=1'
    - '-DCFG_SLEEP_MODE=PWR_MODE_NO_SLEEP'
    - '-DCFG_HEARTBEAT_MODE=0'
    - '-DPHY_MCU_TYPE=MCU_BUMBEE_M0'
    - '-DUSE_FS=0'
    - '-DMAX_NUM_LL_CONN=1'
    - '-DGATT_MAX_NUM_CONN=MAX_NUM_LL_CONN+1'
    - '-DGATT_PVNR=0'
    - '-DMESH_HEAP=0'
    - '-D_RTE_'
    - '-D__CC_ARM'
    - '-D__arm__'
    - '-D__align(x)='
    - '-D__ALIGNOF__(x)='
    - '-D__alignof__(x)='
    - '-D__asm(x)='
    - '-D__forceinline='
    - '-D__restrict='
    - '-D__global_reg(n)='
    - '-D__inline='
    - '-D__int64=long long'
    - '-D__INTADDR__(expr)=0'
    - '-D__irq='
    - '-D__packed='
    - '-D__pure='
    - '-D__smc(n)='
    - '-D__svc(n)='
    - '-D__svc_indirect(n)='
    - '-D__svc_indirect_r7(n)='
    - '-D__value_in_regs='
    - '-D__weak='
    - '-D__writeonly='
    - '-D__declspec(x)='
    - '-D__attribute__(x)='
    - '-D__nonnull__(x)='
    - '-D__register='
    - '-D__breakpoint(x)='
    - '-D__cdp(x,y,z)='
    - '-D__clrex()='
    - '-D__clz(x)=0U'
    - '-D__current_pc()=0U'
    - '-D__current_sp()=0U'
    - '-D__disable_fiq()='
    - '-D__disable_irq()='
    - '-D__dmb(x)='
    - '-D__dsb(x)='
    - '-D__enable_fiq()='
    - '-D__enable_irq()='
    - '-D__fabs(x)=0.0'
    - '-D__fabsf(x)=0.0f'
    - '-D__force_loads()='
    - '-D__force_stores()='
    - '-D__isb(x)='
    - '-D__ldrex(x)=0U'
    - '-D__ldrexd(x)=0U'
    - '-D__ldrt(x)=0U'
    - '-D__memory_changed()='
    - '-D__nop()='
    - '-D__pld(...)='
    - '-D__pli(...)='
    - '-D__qadd(x,y)=0'
    - '-D__qdbl(x)=0'
    - '-D__qsub(x,y)=0'
    - '-D__rbit(x)=0U'
    - '-D__rev(x)=0U'
    - '-D__return_address()=0U'
    - '-D__ror(x,y)=0U'
    - '-D__schedule_barrier()='
    - '-D__semihost(x,y)=0'
    - '-D__sev()='
    - '-D__sqrt(x)=0.0'
    - '-D__sqrtf(x)=0.0f'
    - '-D__ssat(x,y)=0'
    - '-D__strex(x,y)=0U'
    - '-D__strexd(x,y)=0'
    - '-D__strt(x,y)='
    - '-D__swp(x,y)=0U'
    - '-D__usat(x,y)=0U'
    - '-D__wfe()='
    - '-D__wfi()='
    - '-D__yield()='
    - '-D__vfp_status(x,y)=0'
  Compiler: C:\Keil_v5\ARM\ARMCC\bin\armcc.exe
