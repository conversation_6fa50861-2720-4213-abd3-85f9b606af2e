<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.9.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x20000) IROM(0x00000000,0x40000) CPUTYPE("Cortex-M0") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0NEW_DEVICE -FS00 -FL040000 -FP0($$Device:ARMCM0$Device\ARM\Flash\NEW_DEVICE.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0$Device\ARM\ARMCM0\Include\ARMCM0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM0$Device\ARM\SVD\ARMCM0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>mesh_gateway</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>fromelf.exe --bin -o  ./mesh_gateway.bin  ./Objects/mesh_gateway.axf</UserProg1Name>
            <UserProg2Name>fromelf -c -a -d -e -v -o mesh_gateway.asm ./Objects/mesh_gateway.axf</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>1</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4>.\ram.ini</Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x400</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x1fff8000</StartAddress>
                <Size>0x1f40</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x1fff6000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-DADV_NCONN_CFG=0x01  -DADV_CONN_CFG=0x02  -DSCAN_CFG=0x04   -DINIT_CFG=0x08   -DBROADCASTER_CFG=0x01 -DOBSERVER_CFG=0x02  -DPERIPHERAL_CFG=0x04  -DCENTRAL_CFG=0x08 -DHOST_CONFIG=0x0c</MiscControls>
              <Define>CFG_CP, OSAL_CBTIMER_NUM_TASKS=1, MTU_SIZE=247 OSALMEM_METRICS=0 DEBUG_INFO=1 CFG_SLEEP_MODE=PWR_MODE_NO_SLEEP CFG_HEARTBEAT_MODE=0 PHY_MCU_TYPE=MCU_BUMBEE_M0 USE_FS=0 MAX_NUM_LL_CONN=1 GATT_MAX_NUM_CONN=MAX_NUM_LL_CONN+1 GATT_PVNR=0 MESH_HEAP=0</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\components\inc;..\..\..\components\ble\controller\include;..\..\..\components\osal\include;..\..\..\components\common;..\..\..\components\ble\include;..\..\..\components\ble\hci;..\..\..\components\ble\host;..\..\..\components\Profiles\DevInfo;..\..\..\components\Profiles\SimpleProfile;..\..\..\components\Profiles\Roles;..\..\..\components\driver\common;..\..\..\components\driver\clock;..\..\..\components\driver\gpio;..\..\..\components\driver\pwrmgr;..\..\..\components\driver\log;..\..\..\components\driver\uart;..\..\..\components\driver\flash;..\..\..\components\driver\pwm;.\source;.\source\bleMesh;..\..\..\components\ethermind\external\crypto\aes;..\..\..\components\ethermind\mesh\export\include;..\..\..\components\ethermind\mesh\export\bearer;..\..\..\components\ethermind\mesh\export\platforms\ext;..\..\..\components\ethermind\osal\src\phyos;..\..\..\components\ethermind\utils\include;..\..\..\components\ethermind\mesh\export\appl;..\..\..\components\driver\pwrmgr;..\..\..\components\osal\snv;..\..\..\components\ethermind\external\crypto\asm_ecdh_p256;..\..\..\components\ethermind\lib;..\..\..\components\ethermind\mesh\export\cbtimer;..\..\..\components\arch\cm0;..\..\..\misc;..\..\..\components\ble\controller;..\..\..\components\libraries\crc16;..\..\..\components\driver\timer;..\..\..\components\ethermind\mesh\export\climodel;..\..\..\components\libraries\fs;..\..\..\components\libraries\cli;..\..\..\components\ethermind\mesh\export\vendormodel;..\..\..\components\ethermind\mesh\export\vendormodel\client;..\..\..\components\ethermind\mesh\export\vendormodel\server;..\..\..\components\ethermind\platforms;..\..\..\components\ethermind\platforms\interfaces\crypto;..\..\..\components\ethermind\platforms\mesh;..\..\..\components\driver\led_light</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>0</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0</TextAddressRange>
            <DataAddressRange>0</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\scatter_load.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>..\..\..\misc\bb_rom_sym_m0.txt  --keep=jump_table_base  --keep=global_config</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>arch</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\source\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>driver</GroupName>
          <Files>
            <File>
              <FileName>pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\pwm\pwm.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\flash\flash.c</FilePath>
            </File>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\clock\clock.c</FilePath>
            </File>
            <File>
              <FileName>my_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\log\my_printf.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\gpio\gpio.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\uart\uart.c</FilePath>
            </File>
            <File>
              <FileName>pwrmgr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\pwrmgr\pwrmgr.c</FilePath>
            </File>
            <File>
              <FileName>dongleKey.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\source\bleMesh\dongleKey.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\libraries\crc16\crc16.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>profile</GroupName>
          <Files>
            <File>
              <FileName>devinfoservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\DevInfo\devinfoservice.c</FilePath>
            </File>
            <File>
              <FileName>gattservapp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\GATT\gattservapp.c</FilePath>
            </File>
            <File>
              <FileName>peripheral.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\Roles\peripheral.c</FilePath>
            </File>
            <File>
              <FileName>gapgattserver.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\Roles\gapgattserver.c</FilePath>
            </File>
            <File>
              <FileName>gap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\Roles\gap.c</FilePath>
            </File>
            <File>
              <FileName>observer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\Roles\observer.c</FilePath>
            </File>
            <File>
              <FileName>central.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\Roles\central.c</FilePath>
            </File>
            <File>
              <FileName>gapbondmgr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\profiles\Roles\gapbondmgr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>jump</GroupName>
          <Files>
            <File>
              <FileName>jump_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\misc\jump_table.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>osal</GroupName>
          <Files>
            <File>
              <FileName>osal_snv.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\osal\snv\osal_snv.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>blemesh</GroupName>
          <Files>
            <File>
              <FileName>bleMesh.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\source\bleMesh\bleMesh.c</FilePath>
            </File>
            <File>
              <FileName>bleMesh_Main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\source\bleMesh\bleMesh_Main.c</FilePath>
            </File>
            <File>
              <FileName>OSAL_bleMesh.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\source\bleMesh\OSAL_bleMesh.c</FilePath>
            </File>
            <File>
              <FileName>led_light.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\driver\led_light\led_light.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ethermind</GroupName>
          <Files>
            <File>
              <FileName>EM_platform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\platforms\EM_platform.c</FilePath>
            </File>
            <File>
              <FileName>aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\external\crypto\aes\aes.c</FilePath>
            </File>
            <File>
              <FileName>aes-ccm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\external\crypto\aes\aes-ccm.c</FilePath>
            </File>
            <File>
              <FileName>EM_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\osal\src\phyos\EM_debug.c</FilePath>
            </File>
            <File>
              <FileName>EM_os.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\osal\src\phyos\EM_os.c</FilePath>
            </File>
            <File>
              <FileName>EM_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\osal\src\phyos\EM_timer.c</FilePath>
            </File>
            <File>
              <FileName>blebrr_pl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\platforms\mesh\blebrr_pl.c</FilePath>
            </File>
            <File>
              <FileName>cry.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\platforms\interfaces\crypto\cry.c</FilePath>
            </File>
            <File>
              <FileName>blebrr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\bearer\blebrr.c</FilePath>
            </File>
            <File>
              <FileName>blebrr_gatt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\bearer\blebrr_gatt.c</FilePath>
            </File>
            <File>
              <FileName>MS_common_pl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\platforms\ext\MS_common_pl.c</FilePath>
            </File>
            <File>
              <FileName>prov_pl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\platforms\ext\prov_pl.c</FilePath>
            </File>
            <File>
              <FileName>mesh_services.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\platforms\mesh\mesh_services.c</FilePath>
            </File>
            <File>
              <FileName>appl_prov.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\appl\appl_prov.c</FilePath>
            </File>
            <File>
              <FileName>appl_proxy.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\appl\appl_proxy.c</FilePath>
            </File>
            <File>
              <FileName>mesh_clients.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\platforms\mesh\mesh_clients.c</FilePath>
            </File>
            <File>
              <FileName>EXT_cbtimer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\cbtimer\EXT_cbtimer.c</FilePath>
            </File>
            <File>
              <FileName>cli_model.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\climodel\cli_model.c</FilePath>
            </File>
            <File>
              <FileName>access_ps.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\access_ps\access_ps.c</FilePath>
            </File>
            <File>
              <FileName>MS_limit_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\common\MS_limit_config.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>mesh_lib</GroupName>
          <Files>
            <File>
              <FileName>libethermind_mesh_core.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\components\ethermind\lib\meshlibs\phyos\keil\libethermind_mesh_core.lib</FilePath>
            </File>
            <File>
              <FileName>libethermind_ecdh.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\components\ethermind\lib\meshlibs\phyos\keil\libethermind_ecdh.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>libethermind_mesh_models.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\components\ethermind\lib\meshlibs\phyos\keil\libethermind_mesh_models.lib</FilePath>
            </File>
            <File>
              <FileName>libethermind_utils.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\components\ethermind\lib\meshlibs\phyos\keil\libethermind_utils.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>samples</GroupName>
          <Files>
            <File>
              <FileName>appl_sample_mesh_gateway.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\source\bleMesh\appl_sample_mesh_gateway.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>vendormodel</GroupName>
          <Files>
            <File>
              <FileName>vendormodel_client.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\ethermind\mesh\export\vendormodel\client\vendormodel_client.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lib</GroupName>
          <Files>
            <File>
              <FileName>ble_host.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\lib\ble_host.lib</FilePath>
            </File>
            <File>
              <FileName>rf.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\lib\rf.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>cli</GroupName>
          <Files>
            <File>
              <FileName>cliface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\libraries\cli\cliface.c</FilePath>
            </File>
            <File>
              <FileName>cliface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\components\libraries\cli\cliface.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
        <Group>
          <GroupName>::Device</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>2</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="4.3.0" condition="Cortex-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
      <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.0.1" condition="ARMCM0 CMSIS">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="sourceAsm" condition="ARMCC" name="Device\ARM\ARMCM0\Source\ARM\startup_ARMCM0.s" version="1.0.0">
        <instance index="0">RTE\Device\ARMCM0\startup_ARMCM0.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.2" condition="ARMCM0 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.7.7" url="http://www.keil.com/pack/" vendor="ARM" version="5.9.0"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </file>
      <file attr="config" category="sourceC" name="Device\ARM\ARMCM0\Source\system_ARMCM0.c" version="1.0.0">
        <instance index="0">RTE\Device\ARMCM0\system_ARMCM0.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.2" condition="ARMCM0 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.7.7" url="http://www.keil.com/pack/" vendor="ARM" version="5.9.0"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </file>
      <file attr="config" category="sourceAsm" condition="ARMCC" name="Device\ARM\ARMCM4\Source\ARM\startup_ARMCM4.s" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM4\startup_ARMCM4.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.0.1" condition="ARMCM4 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="sourceC" name="Device\ARM\ARMCM4\Source\system_ARMCM4.c" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM4\system_ARMCM4.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.0.1" condition="ARMCM4 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="sourceAsm" condition="ARMCC" name="Device\ARM\ARMCM4\Source\ARM\startup_ARMCM4.s" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM4_FP\startup_ARMCM4.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.0.1" condition="ARMCM4 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="sourceC" name="Device\ARM\ARMCM4\Source\system_ARMCM4.c" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM4_FP\system_ARMCM4.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.0.1" condition="ARMCM4 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>&lt;Project Info&gt;</LayName>
        <LayDesc></LayDesc>
        <LayUrl></LayUrl>
        <LayKeys></LayKeys>
        <LayCat></LayCat>
        <LayLic></LayLic>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
